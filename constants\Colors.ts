// Ce fichier est maintenant géré dynamiquement par le ThemeContext
// Mais on garde une version statique pour la compatibilité

export default {
  primary: {
    50: '#F3E5FF',
    100: '#E7CCFE',
    200: '#D0A9FD',
    300: '#B985FB',
    400: '#A262FA',
    500: '#8A2BE2', // Main primary color
    600: '#7325BA',
    700: '#5C1E93',
    800: '#46176C',
    900: '#2F0F45',
  },
  accent: {
    300: '#E9D5FF',
    500: '#9370DB', // Secondary purple
    700: '#6A5ACD',
  },
  success: {
    50: '#ECFDF5',
    300: '#6EE7B7',
    500: '#10B981',
    700: '#047857',
  },
  warning: {
    50: '#FFFBEB',
    300: '#FCD34D',
    500: '#F59E0B',
    700: '#B45309',
  },
  error: {
    50: '#FEF2F2',
    300: '#FCA5A5',
    500: '#EF4444',
    700: '#B91C1C',
  },
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
  background: {
    light: '#FFFFFF',
    dark: '#111827',
  },
};