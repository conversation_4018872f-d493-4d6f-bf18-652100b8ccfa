import { useEffect, useRef } from 'react';
import { Audio } from 'expo-av';
import { Platform } from 'react-native';

interface SoundEffects {
  playMessageSent: () => Promise<void>;
  playMessageReceived: () => Promise<void>;
  playNotification: () => Promise<void>;
}

export function useSoundEffects(): SoundEffects {
  const messageSentSoundRef = useRef<Audio.Sound | null>(null);
  const messageReceivedSoundRef = useRef<Audio.Sound | null>(null);
  const notificationSoundRef = useRef<Audio.Sound | null>(null);
  const isLoadedRef = useRef(false);

  useEffect(() => {
    loadSounds();
    return () => {
      unloadSounds();
    };
  }, []);

  const loadSounds = async () => {
    try {
      // Pour le web, on utilise directement Web Audio API
      if (Platform.OS === 'web') {
        isLoadedRef.current = true;
        return;
      }

      // Configurer le mode audio pour mobile uniquement
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Créer des sons simples pour mobile
      try {
        // Son pour message envoyé
        const { sound: sentSound } = await Audio.Sound.createAsync(
          require('../assets/sounds/message-sent.mp3'), // Vous devrez ajouter ces fichiers
          { shouldPlay: false, volume: 0.3 }
        );
        messageSentSoundRef.current = sentSound;
      } catch {
        // Fallback si le fichier n'existe pas
        console.log('Fichier son message envoyé non trouvé, utilisation du fallback');
      }

      try {
        // Son pour message reçu
        const { sound: receivedSound } = await Audio.Sound.createAsync(
          require('../assets/sounds/message-received.mp3'),
          { shouldPlay: false, volume: 0.4 }
        );
        messageReceivedSoundRef.current = receivedSound;
      } catch {
        console.log('Fichier son message reçu non trouvé, utilisation du fallback');
      }

      try {
        // Son de notification
        const { sound: notificationSound } = await Audio.Sound.createAsync(
          require('../assets/sounds/notification.mp3'),
          { shouldPlay: false, volume: 0.5 }
        );
        notificationSoundRef.current = notificationSound;
      } catch {
        console.log('Fichier son notification non trouvé, utilisation du fallback');
      }

      isLoadedRef.current = true;

    } catch (error) {
      console.log('Erreur lors du chargement des sons:', error);
      // Utiliser les fallbacks web même sur mobile en cas d'erreur
      isLoadedRef.current = true;
    }
  };

  const unloadSounds = async () => {
    try {
      if (messageSentSoundRef.current) {
        await messageSentSoundRef.current.unloadAsync();
        messageSentSoundRef.current = null;
      }
      if (messageReceivedSoundRef.current) {
        await messageReceivedSoundRef.current.unloadAsync();
        messageReceivedSoundRef.current = null;
      }
      if (notificationSoundRef.current) {
        await notificationSoundRef.current.unloadAsync();
        notificationSoundRef.current = null;
      }
    } catch (error) {
      console.log('Erreur lors du déchargement des sons:', error);
    }
  };

  const playMessageSent = async () => {
    try {
      if (!isLoadedRef.current) {
        console.log('Sons non encore chargés');
        return;
      }

      if (Platform.OS === 'web') {
        playWebTone(800, 0.1, 0.3);
        return;
      }

      if (messageSentSoundRef.current) {
        const status = await messageSentSoundRef.current.getStatusAsync();
        if (status.isLoaded) {
          await messageSentSoundRef.current.replayAsync();
        } else {
          playWebTone(800, 0.1, 0.3);
        }
      } else {
        playWebTone(800, 0.1, 0.3);
      }
    } catch (error) {
      console.log('Erreur lors de la lecture du son d\'envoi:', error);
      // Fallback silencieux
    }
  };

  const playMessageReceived = async () => {
    try {
      if (!isLoadedRef.current) {
        console.log('Sons non encore chargés');
        return;
      }

      if (Platform.OS === 'web') {
        playWebTone(400, 0.2, 0.4);
        return;
      }

      if (messageReceivedSoundRef.current) {
        const status = await messageReceivedSoundRef.current.getStatusAsync();
        if (status.isLoaded) {
          await messageReceivedSoundRef.current.replayAsync();
        } else {
          playWebTone(400, 0.2, 0.4);
        }
      } else {
        playWebTone(400, 0.2, 0.4);
      }
    } catch (error) {
      console.log('Erreur lors de la lecture du son de réception:', error);
      // Fallback silencieux
    }
  };

  const playNotification = async () => {
    try {
      if (!isLoadedRef.current) {
        console.log('Sons non encore chargés');
        return;
      }

      if (Platform.OS === 'web') {
        playWebNotification();
        return;
      }

      if (notificationSoundRef.current) {
        const status = await notificationSoundRef.current.getStatusAsync();
        if (status.isLoaded) {
          await notificationSoundRef.current.replayAsync();
        } else {
          playWebNotification();
        }
      } else {
        playWebNotification();
      }
    } catch (error) {
      console.log('Erreur lors de la lecture de la notification:', error);
      // Fallback silencieux
    }
  };

  return {
    playMessageSent,
    playMessageReceived,
    playNotification,
  };
}

// Fonctions Web Audio API pour le web
function playWebTone(frequency: number, duration: number, volume: number) {
  if (typeof window === 'undefined') return;
  
  try {
    // Vérifier si AudioContext est disponible
    const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
    if (!AudioContext) {
      console.log('Web Audio API non disponible');
      return;
    }

    const audioContext = new AudioContext();
    
    // Reprendre le contexte audio si nécessaire (politique des navigateurs)
    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    
    // Envelope pour éviter les clics
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
    
    // Nettoyer après utilisation
    setTimeout(() => {
      try {
        audioContext.close();
      } catch (e) {
        // Ignorer les erreurs de nettoyage
      }
    }, (duration + 0.1) * 1000);
    
  } catch (error) {
    console.log('Erreur Web Audio API:', error);
  }
}

function playWebNotification() {
  if (typeof window === 'undefined') return;
  
  try {
    const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
    if (!AudioContext) {
      console.log('Web Audio API non disponible');
      return;
    }

    const audioContext = new AudioContext();
    
    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }
    
    // Mélodie simple: Do-Mi-Sol
    const notes = [523.25, 659.25, 783.99]; // C5, E5, G5
    const noteDuration = 0.15;
    
    notes.forEach((frequency, index) => {
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = 'sine';
      
      const startTime = audioContext.currentTime + index * noteDuration;
      const endTime = startTime + noteDuration;
      
      gainNode.gain.setValueAtTime(0, startTime);
      gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, endTime);
      
      oscillator.start(startTime);
      oscillator.stop(endTime);
    });
    
    // Nettoyer après utilisation
    setTimeout(() => {
      try {
        audioContext.close();
      } catch (e) {
        // Ignorer les erreurs de nettoyage
      }
    }, (notes.length * noteDuration + 0.1) * 1000);
    
  } catch (error) {
    console.log('Erreur Web Audio API notification:', error);
  }
}